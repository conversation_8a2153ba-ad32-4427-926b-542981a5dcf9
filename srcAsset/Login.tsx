import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { navigationRef } from '../src/navigation/Navigation';
import { httpPost } from './utils/http';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StackActions } from '@react-navigation/native';
import { Alert } from 'react-native';
import ErrorHandlerPopup from '../src/component/ErrorHandlerPopup';
import Spinner from "../src/component/Loader";
import { stringText } from '../src/utils/stringConstants';



const Login = (props: any) => {
    const { navigation } = props;
    const [errorMsg, setErrorMsg] = useState(null);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
        const [loader, setLoader] = useState<boolean>(false);

    useEffect(() => {
        GoogleSignin.configure({
            webClientId: '741974248997-bjral2852718ogfdn3kd959butlobjch.apps.googleusercontent.com',
            offlineAccess: true,
        });
        stayLogin();
    }, []);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    const stayLogin = async () => {
        setLoader(true);
        const usertoken = await AsyncStorage.getItem("AuthToken");
        if (usertoken !== null) {
          setLoader(false);
          navigation.dispatch(StackActions.replace("Home"));
        } else {
          setLoader(false);
        }
      };

    const handleGoogleSignIn = async () => {
        try {
            const userInfo = await GoogleSignin.signIn();
            const googleCredential = await GoogleSignin.getTokens();
            if (userInfo && googleCredential) {
                const tudipEmailRegex = /@tudip\.com$/;

                const email = userInfo.user.email;
     
                if (tudipEmailRegex.test(email)) {
                    UserTokenAPI(googleCredential.accessToken);
                } else {
                    GoogleSignin.revokeAccess();
                    GoogleSignin.signOut();
                    errorHandlerClicked(true, "Please use tudip email only")
                }

                // UserTokenAPI(googleCredential.accessToken);

                // navigation.navigate('Home')
                // setIsAuthenticated(true);
                // setRes(JSON.stringify(userInfo));
            }
        } catch (error) {
            // console.log(error);
            setErrorMsg(JSON.stringify(error));
            console.error('Google Sign-In Error:', error);
        }
    };

    const UserTokenAPI = (token) => {
        httpPost(`google-authentication`, { token })
            .then(async (response: any) => {


                const responseData = JSON.parse(response).data
                
                AsyncStorage.setItem('AuthToken', responseData.access_token).finally(

                    navigation.dispatch(StackActions.replace("Home"))

                );

                // setAllEmployees(responseData);
                // setData(responseData);
            })
            .catch((err: any) => {
                errorHandlerClicked(
                    true,
                    `${err}`
                  );
                //   AsyncStorage.clear();
            });

    }
    return (
        <View style={styles.googleButtonView}>
            <View style={styles.kuberImage}>
                <Image
                    source={require("../src/assets/Images/logokuber.png")}
                    resizeMode="contain"
                    style={styles.imageView}
                />
            </View>
            <TouchableOpacity
                style={styles.googleButton}
                onPress={() => {
                    // navigation.navigate('Home')
                    handleGoogleSignIn();
                    // isAuthenticated ? navigation.navigate('Home') : navigation.navigate('GoogleLoginButton')
                    // Handle Google Sign-In here
                }}>
                <View style={styles.iconContainer}>
                    <MaterialCommunityIcons
                        name="google"
                        color="#fff" // White color for the Google icon
                        size={30}
                    />
                </View>
                <View style={styles.textContainer}>
                    <Text style={styles.buttonText}>Login with Tudip Gmail</Text>
                </View>
            </TouchableOpacity>
            <Spinner animating={loader} />
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    googleButtonView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        width: 200,
        height: 50,
        alignSelf: 'center',
    },
    googleButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#193c6c', // Google red
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 5,
        padding: 20,
        marginTop: 20,
    },
    iconContainer: {
        marginRight: 15,
    },
    textContainer: {
        flex: 1,
    },
    buttonText: {
        color: '#fff', // White color for text
        fontSize: 18,
    },
    imageView: {
        height: 65,
        width: 166,
        // alignSelf: "center",
        marginTop: 90,
    },
    kuberImage: {
        marginBottom: 80,
    },
});

export default Login;
