import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import * as Animatable from 'react-native-animatable';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { color } from '../src/utils/color';

const SuccessScreen = (props: any) => {
  const animationRef = useRef(null);
  const { navigation } = props;

  useEffect(() => {
    // Start the animation when the component is mounted
    if (animationRef.current) {
      animationRef.current.zoomIn(1000); // Customize the animation as needed
    }
  }, []);

  return (
    <View style={styles.container}>
      <Animatable.View ref={animationRef} style={styles.successContainer} animation="zoomIn" duration={1000}>
        <MaterialCommunityIcons
          name="alert-circle-check-outline"
          color={color.DARK_GREEN}
          size={80}
          style={{
            justifyContent: 'center',
            alignSelf: 'center'
          }}
        />
        <Text style={styles.successText}>Successfully Updated</Text>
      </Animatable.View>
      <TouchableOpacity style={styles.backButton}
        onPress={() => {
          navigation.navigate('Home');

        }}
      >
        <MaterialCommunityIcons
          name="arrow-left"
          color={color.WHITE}
          size={40}
          style={{
            justifyContent: 'center',
            alignSelf: 'center'
          }}
        />
      </TouchableOpacity>
      {/* <Text style={styles.textBack}>Go To Home Screen</Text> */}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
  },
  successText: {
    fontSize: 24,
    color: 'black',
    fontWeight: 'bold',
  },
  backButton: {
    marginTop: 50,
    backgroundColor: color.DARK_BLUE,
    borderRadius: 50,
    padding: 5,
    margin: 10
  },
  textBack: {
    color: 'black'
  }
});

export default SuccessScreen;
