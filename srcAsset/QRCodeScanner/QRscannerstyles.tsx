import { StyleSheet, Dimensions } from "react-native";

const window = Dimensions.get("window");
const screen = Dimensions.get("screen");

export const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textViewStyle: {
    flexDirection: 'row',
  },
  iconStyle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconStyleview: {
    marginLeft: 20,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  marker: {
    // borderRadius: 10,
    // borderWidth: 3,
    borderColor: "white",
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.0)',
    borderTopWidth: 5
  },
  marker1: {
    // borderRadius: 10,
    // borderWidth: 3,
    marginTop: 10,
    borderColor: "#193C6D",
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.0)',
    borderTopWidth: 5
  },
  cameraTimeoutView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraTimeoutText: {
    color: 'black',
    fontSize: 30,
    fontWeight: 'bold',
  },
  modalView: {
    margin: 20,
    justifyContent: 'center',
    alignItems: 'center',
    // marginTop: 200
  },
  modalViewSucces: {
    margin: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 400
  },
  modalTextSuccess: {
    color: 'green',
    fontSize: 30,
    fontWeight: 'bold',

  },
  modalText: {
    color: 'white',
    fontSize: 30,
    fontWeight: 'bold',
    marginBottom: 10,
    padding: 5,
    // backgroundColor:'#193C6D',
    // borderRadius:3,
    marginLeft: 10
    // alignSelf:'center'
  },
  modalTextView: {
    // alignItems: "center",
    // height:"75%",
    // width:350,
    // flexDirection: "column",
    // flex:1,
    // justifyContent:'center',
    // alignItems:'center',
    // verticalAlign:'middle',
    // backgroundColor:'red',
    // alignSelf:'center',
    // marginTop:"20%",
    // alignContent:'center',
    marginLeft: window.width > 500 ? 20 : 15,
    marginTop: window.width > 500 ? 10 : 25,
  },
  assigneeText: {
    padding: 10
  },
  modalTextsmall: {
    color: 'grey',
    fontSize: window.width > 500 ? 15 : 12,
    marginBottom: 10,
    padding: 3.5,
    paddingLeft: 5,
    fontWeight: "500",
    //   display: 'flex',
    // flexDirection: 'row',
    // alignItems: 'center',

    // backgroundColor:'blue',
    width: window.width > 500 ? 120 : 100
  },
  modalTextsmallright: {
    color: '#193C6D',
    fontSize: window.width > 500 ? 15 : 12,
    // marginLeft:70,
    // marginBottom: 10,
    // padding:4,
    // paddingRight:5,
    fontWeight: "500",
    // backgroundColor:'red',
    // alignSelf:'flex-end'
    // margin:20
  },
  assetHeaderText: {
    color: '#193C6D',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    padding: 12,
    // backgroundColor:'#193C6D'
  },
  modalTextsmallEmp: {
    color: '#193C6D',
    fontSize: 15,
    marginBottom: 10,
    // padding:5,
    fontWeight: "600",
    // backgroundColor:''
  },
  closeButtonText: {
    alignContent: "center",
    alignSelf: "center",
    fontWeight: "400",
    fontSize: 14,
    fontFamily: "Poppins-Regular",
    color: 'white'
  },
  inputContainer: {
    width: '80%',
    marginTop: 20,
  },
  input: {
    width: '100%',
    height: 45,
    borderColor: '#193C6D',
    borderWidth: 1,
    paddingHorizontal: 10,
    borderRadius: 10,
    color: 'black',
    backgroundColor: 'white',
  },
  updateMessage: {
    color: 'green',
    fontSize: 25,
    marginTop: 10,
    marginBottom: 10
  },
  containerButton: {
    display: "flex",
    marginTop: 20,
    marginBottom: 10,
    borderWidth: 2,
    width: 170,
    height: 50,
    backgroundColor: "#193C6D",
    alignSelf: "center",
    borderColor: 'white',
    borderRadius: 15,
    justifyContent: "center",
  },
  containerButtonUn: {
    display: "flex",
    marginTop: 20,
    marginBottom: 10,
    borderWidth: 2,
    width: 170,
    height: 50,
    backgroundColor: "white",
    alignSelf: "center",
    borderColor: 'white',
    borderRadius: 15,
    justifyContent: "center",
  },
  buttonText: {
    alignContent: "center",
    alignSelf: "center",
    fontWeight: "400",
    fontSize: 14,
    fontFamily: "Poppins-Regular",
    color: 'white'
  },
  buttonTextUn: {
    alignContent: "center",
    alignSelf: "center",
    fontWeight: "400",
    fontSize: 14,
    fontFamily: "Poppins-Regular",
    color: 'black'
  },
  cardContainer: {
    // marginTop: 100,
    flexDirection: "column",
    // backgroundColor: '#193C6D',
    backgroundColor: "white",
    borderRadius: 8,
    // padding: 40,
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 20,
    borderWidth: 1.3,
    borderColor: "#193C6D",
    margin: 10,
    height: window.height > 1000 ? "52%" : "62%",
    width: window.width > 600 ? 500 : 358,
  },
  cardItem: {
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  slideInDownText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    borderBottomWidth: 2
  },
  slideInDownTextContainer: {
  },
  containerMarker: {
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderRightWidth: 5,
    borderLeftWidth: 5,
    borderTopWidth: 5,
    borderColor: 'transparent',
  },
  containerMarker1: {
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    borderRightWidth: 5,
    borderLeftWidth: 5,
    borderBottomWidth: 5,
    borderColor: 'transparent',
  },
  cornerTopLeft: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: window.width > 500 ? 100 : 50,
    height: window.width > 500 ? 100 : 50,
    borderTopLeftRadius: 10,
    borderTopWidth: 5,
    borderLeftWidth: 5,
    borderBottomLeftRadius: 1,
    borderColor: 'white',
  },
  cornerTopRight: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: window.width > 500 ? 100 : 50,
    height: window.width > 500 ? 100 : 50,
    borderTopRightRadius: 10,
    borderTopWidth: 5,
    borderRightWidth: 5,
    borderBottomRightRadius: 1,
    borderColor: 'white',
  },
  cornerBottomLeft: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: window.width > 500 ? 100 : 50,
    height: window.width > 500 ? 100 : 50,
    borderBottomLeftRadius: 10,
    borderBottomWidth: 5,
    borderLeftWidth: 5,
    borderColor: 'white',
  },
  cornerBottomRight: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: window.width > 500 ? 100 : 50,
    height: window.width > 500 ? 100 : 50,
    borderBottomRightRadius: 10,
    borderBottomWidth: 5,
    borderRightWidth: 5,
    borderColor: 'white',
  },
  headingViewStyle: {
    backgroundColor: '#193C6D',
    // borderRadius:3,
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3
  }
});