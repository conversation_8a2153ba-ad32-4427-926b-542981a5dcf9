import React, { useEffect, useState } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Modal,
  Keyboard,
  Alert,
  Dimensions
} from 'react-native';
import QRCodeScanner from 'react-native-qrcode-scanner';
import SuccessPopUp from '../../src/component/SuccessPopUp';
import Spinner from '../../src/component/Loader';
import * as Animatable from 'react-native-animatable';
import { styles } from './QRscannerstyles';
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { color } from '../../src/utils/color';
import { httpGet } from '../utils/http';
import ErrorHandlerPopup from '../../src/component/ErrorHandlerPopup';
import { stringText } from '../../src/utils/stringConstants';
import Entypo from 'react-native-vector-icons/Entypo';

const QRCodeScannerView = (props: any) => {

  const { navigation } = props;

  const [scannedData, setScannedData] = useState();
  const [enteredID, setEnteredID] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [updateMessage, setUpdateMessage] = useState('');
  const [showPopUp, setShowPopUp] = useState<boolean>(true);
  const [orderSuccessPopupVisibility, setOrderSuccessPopupVisibility] = useState<boolean>(false);
  const [orderSuccessPopupMessage, setOrderSuccessPopupMessage] = useState<string>("");
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [loader, setLoader] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [empAssignedShowText, setEmpAssignedShowText] = useState<string>("");
  const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
  const [errorHandlerVisibility, setErrorHandlerVisibility] =
    useState<boolean>(false);
  const [assignStatus, setAssignStatus] =
    useState('');

  const [isassignedlogin, setIsAssigneLogin] = useState();

  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

  const markerWidthPercentage = screenWidth > 500 ? 50 : 70;
  const markerHeightPercentage = screenWidth > 500 ? 25 : 35;

  const markerWidth = (Math.min(screenWidth, screenHeight) * markerWidthPercentage) / 100;
  const markerHeight = (Math.min(screenWidth, screenHeight) * markerHeightPercentage) / 100;

  const window = Dimensions.get('window');

  useEffect(() => {
    if (showSuccessModal) {
      const timeoutId = setTimeout(() => {
        closeSuccessModal();
      }, 2000);

      return () => clearTimeout(timeoutId);
    }

  }, [showSuccessModal]);

  const handleScan = (e: any) => {
    console.log("e===>>", e.data);
    // console.log("scannedValue=====>>>>.", e.hasOwnProperty('data'));
    // const dataValue = e.data;
    try {
      const scannedValue = JSON.parse(e.data);
      // console.log("e.data", e.data);
      // console.log("scannedValue=====>>>>.", scannedValue.hasOwnProperty('laptopID'));
      if (
        // dataValue &&
        scannedValue &&
        (scannedValue['laptopID'] != undefined ||
          scannedValue['laptopOS'] != undefined ||
          scannedValue['make'] != undefined ||
          scannedValue['adaptorNo'] != undefined ||
          scannedValue['serviceTag'] != undefined ||
          scannedValue['hardDisk'] != undefined ||
          scannedValue['ram'] != undefined
        )) {

        // setLoader(true);

        if (scannedValue) {
          setScannedData(scannedValue);
          setShowModal(true);
          setLoader(false);
          setIsScanning(true);
          EmployeeInfo(scannedValue);
        } else {
          errorHandlerClicked(
            true,
            `${stringText.SomethingWentwrong}`
          );
        }
      } else {
        errorHandlerClicked(
          true,
          `${stringText.SomethingWentwrong}`
        );
      }
    }
    catch (err) {

      errorHandlerClicked(
        true,
        `${stringText.SomethingWentwrong}`
      );
    }
  };

  const orderSuccessPopupClicked = (isVisible: boolean, message: string) => {
    setOrderSuccessPopupVisibility(isVisible);
    setOrderSuccessPopupMessage(message);
    if (!isVisible) {
      setShowPopUp(true);
    }
  };

  const EmployeeInfo = (scannedValue: any) => {

    // if (scannedData) {
    httpGet(`/assignee?lID=${scannedValue["id"]}`)
      .then(async (response: any) => {
        const responseData = JSON.parse(response).data
        // console.log("responseData==>>", responseData);


        setEmpAssignedShowText(responseData)
        setIsAssigneLogin(responseData.isAssigned)
      })
      .catch((err: any) => {
        // console.log("err", err.message);
        setEmpAssignedShowText('')
        // setEmployeeId('');
        // setLaptopId('');
        // setComments('');
        if (
          err?.response?.data?.message !== undefined &&
          err?.response?.data?.message !== null &&
          err?.response?.data?.message !== ""
        ) {
          errorHandlerClicked(true, err?.response?.data?.message);
        } else {
          errorHandlerClicked(
            true,
            `${stringText.SomethingWentwrong}`
          );
        }
      });
    // }
  }
  const errorHandlerClicked = (
    errorHandlerVisibility: boolean,
    errorHandlerMessage: string
  ) => {
    setErrorHandlerVisibility(errorHandlerVisibility);
    setErrorHandlerMessage(errorHandlerMessage);
  };

  const openSuccessModal = () => {
    setShowSuccessModal(true);
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
  };


  const openCustomPopup = (assignStatus: any) => {
    setAssignStatus(assignStatus);
    setOrderSuccessPopupVisibility(true);
    // EmployeeInfo();
  };

  const closeCustomPopup = () => {

    setOrderSuccessPopupVisibility(false)
  };


  const closeModal = () => {
    // setShowModal(false);
    setOrderSuccessPopupVisibility(false)

    // setEnteredID('');
    // setUpdateMessage('');
    Keyboard.dismiss();

  };

  const makeSlideOutTranslation = (translationType: any) => {
    return {
      from: {
        [translationType]: screenHeight * -0.14
      },
      to: {
        [translationType]: screenHeight * 0.15
      }
    };
  }

  return (
    <SafeAreaView style={styles.mainView}>
      {
        (showModal ? (
          <>

            <View style={{
              // flex: 1,
              position: 'absolute',
              top: 0,
              left: 0,
              margin: 20,
              // zIndex: 1,
              // backgroundColor: 'red'

            }}>
              <TouchableOpacity
                onPress={() => navigation.goBack()}
              >
                <Entypo name="chevron-left" color={color.DARK_BLUE} size={30} />
              </TouchableOpacity>
            </View>



            <View style={styles.modalView}>
              {/* <View>
                <Text style={styles.assetHeaderText}>Asset Information</Text>
              </View> */}

              <View style={styles.cardContainer}>
                {scannedData ?
                  <View style={styles.cardItem}>
                    <View style={styles.headingViewStyle}>
                      <Text style={styles.modalText}>{scannedData["laptopID"]}</Text>
                    </View>

                    <View style={styles.iconStyle}>
                      <Entypo name="laptop" color={color.DARK_BLUE} size={window.width <= 500 ? 60 : 110} style={styles.iconStyleview} />

                      <View style={styles.modalTextView} >
                        <View style={styles.textViewStyle}>
                          <Text style={styles.modalTextsmall}>{"Assigned To: "}</Text>
                          <Text style={styles.modalTextsmallright}>{empAssignedShowText["assignee"]}</Text>
                        </View>
                        <View style={styles.textViewStyle}>
                          <Text style={styles.modalTextsmall}>{"OS: "}</Text>
                          <Text style={styles.modalTextsmallright}>{scannedData["laptopOS"]}</Text>
                        </View>
                        <View style={styles.textViewStyle}>
                          <Text style={styles.modalTextsmall}>{"Model: "}</Text>
                          <Text style={styles.modalTextsmallright}>{scannedData["make"]}</Text>
                        </View>
                        <View style={styles.textViewStyle}>
                          <Text style={styles.modalTextsmall}>{"Adaptor No: "}</Text>
                          <Text style={styles.modalTextsmallright}>{scannedData["adaptorNo"]}</Text>
                        </View>
                        <View style={styles.textViewStyle}>
                          <Text style={styles.modalTextsmall}>{"Service Tag: "}</Text>
                          <Text style={styles.modalTextsmallright}>{scannedData["serviceTag"]}</Text>
                        </View>
                        <View style={styles.textViewStyle}>
                          <Text style={styles.modalTextsmall}>{"Hard Disk Size: "}</Text>
                          <Text style={styles.modalTextsmallright}>{scannedData["hardDisk"]}</Text>
                        </View>
                        <View style={styles.textViewStyle}>
                          <Text style={styles.modalTextsmall}>{"Ram Size: "}</Text>
                          <Text style={styles.modalTextsmallright}>{scannedData["ram"]}</Text>
                        </View>
                        {/* <Text style={styles.modalTextsmall}>{"Adaptor No: "}{scannedData["adaptorNo"]}</Text> */}
                        {/* <Text style={styles.modalTextsmall}>{"Service Tag: "} {scannedData["serviceTag"]}</Text> */}
                        {/* <Text style={styles.modalTextsmall}>{"Hard Disk Size: "}{scannedData["hardDisk"]}</Text> */}
                        {/* <Text style={styles.modalTextsmall}>{"Ram Size: "} {scannedData["ram"]}</Text> */}
                      </View>
                    </View>
                  </View> : <View />
                }
              </View>
              {isassignedlogin == false ?
                <>
                  <View style={styles.buttonContainer}>
                    <TouchableOpacity style={[styles.containerButton, { marginRight: 20 }]}
                      onPress={() => openCustomPopup('Assign')}
                    >
                      <Text style={styles.buttonText}>Assign</Text>
                    </TouchableOpacity>
                  </View>

                </> :
                <>
                  <View style={styles.buttonContainer}>
                    <TouchableOpacity style={styles.containerButton}
                      onPress={() => openCustomPopup('Unassign')}
                    >
                      <Text style={styles.buttonText}>Unassign</Text>
                    </TouchableOpacity>
                  </View>
                </>

              }
            </View>
          </>
          // </Modal>
        ) : (
          <>
            <View >
              <QRCodeScanner
                onRead={handleScan}
                showMarker={true}
                customMarker={
                  <>
                    <View style={[styles.containerMarker, { width: markerWidth, height: markerHeight }]}>
                      <View style={styles.cornerTopLeft} />
                      <View style={styles.cornerTopRight} />
                    </View>
                    <Animatable.View
                      animation={makeSlideOutTranslation(
                        "translateY",
                      )}
                      direction="alternate-reverse"
                      easing="linear"
                      duration={1000}
                      iterationCount="infinite"
                      style={[styles.marker, { width: markerWidth - 35 }]}
                    >
                    </Animatable.View>
                    <View style={[styles.containerMarker1, { width: markerWidth, height: markerHeight }]}>
                      <View style={styles.cornerBottomLeft} />
                      <View style={styles.cornerBottomRight} />
                    </View>
                  </>
                }
                reactivate={true}
                permissionDialogMessage="We need permission to access your camera"
                reactivateTimeout={2000}
                cameraTimeout={500000}
                cameraTimeoutView={
                  <View style={styles.cameraTimeoutView}>
                    <Text style={styles.cameraTimeoutText}>Tap here to scan</Text>
                  </View>
                }
              />
            </View>

          </>
        ))
      }

      {/* {showSuccessModal && (
        <Modal
          transparent={true}
          visible={showSuccessModal}
          animationType='none'
          onRequestClose={closeSuccessModal}>
          <View style={styles.overlay}>
            <View style={styles.modalViewSucces}>
              <Text style={styles.modalTextSuccess}>Updated Successfully!</Text>
            </View>
          </View>
        </Modal>
      )} */}
      <SuccessPopUp
        orderSuccessMessage={''}
        showModal={showModal}
        setShowModal={setShowModal}
        successPopupClicked={orderSuccessPopupClicked}
        navigation={navigation}
        visible={orderSuccessPopupVisibility}
        isAssigned={true}
        empAssignedShowText={empAssignedShowText["laptopID"]}
        assignStatus={assignStatus}
        closeModal={closeModal}
        setIsAssigneLogin={setIsAssigneLogin}
        isassignedlogin={isassignedlogin}
        scannedData={scannedData ? scannedData["laptopID"] : null}
      />
      {/* {showModal ?
         : <View />} */}

      <Spinner animating={loader} />
      <ErrorHandlerPopup
        visible={errorHandlerVisibility}
        errorHandlerMessage={errorHandlerMessage}
        errorHandlerClicked={errorHandlerClicked}
      />
    </SafeAreaView>
  );
};


export default QRCodeScannerView;
