import React from "react";
import { SafeAreaView, Text, TouchableOpacity, View, Platform } from "react-native";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { color } from "../../utils/color";
import { stringText } from "../../utils/stringConstants";
import styles from "./stylesProfile";
import LogoutPopup from "../../component/LogOutMessagePopup";

export type Props = {
  namedisplay: any,
  removeData: any,
  isLogoutPopUpVisible: boolean,
  setIsLogoutPopUpVisible: any
}

const ProfileView = (props: Props) => {

  const { namedisplay, removeData, isLogoutPopUpVisible, setIsLogoutPopUpVisible } = props;

  return (
    <SafeAreaView>
      <View>
        <FontAwesome
          name="user-circle"
          color={color.DARK_BLUE}
          size={100}
          style={styles.proImg}
        />
      </View>
      <View>
        <Text style={styles.textName}>{namedisplay}</Text>
      </View>
      {/* <View style={[styles.viewLogout, Platform.isPad && {width: '80%', alignSelf: 'center' }]}>
        <MaterialCommunityIcons name="logout" size={20} color={color.DARK_BLUE} />
        <TouchableOpacity
          style={{ marginLeft: 10 }}
          onPress={() => setIsLogoutPopUpVisible(true)}
        >
          <Text style={styles.textLogout}>{stringText.LogoutText}</Text>
        </TouchableOpacity>
      </View> */}
      <LogoutPopup
      visible={isLogoutPopUpVisible}
      setIsLogoutPopUpVisible={setIsLogoutPopUpVisible}
      removeData={removeData}
      />
    </SafeAreaView>

  )
}

export default ProfileView;