import AsyncStorage from "@react-native-async-storage/async-storage";
import React, { useEffect, useState } from "react";
import ProfileView from "./ProfileView";

const ProfileScreen = (props: any) => {
    const { navigation } = props;
    const [namedisplay, setNamedisplay] = useState<string | null>("");
    const [isLogoutPopUpVisible, setIsLogoutPopUpVisible] = useState<boolean>(false);
  
    const removeData = async () => {
      try {
        await AsyncStorage.removeItem("AuthToken");
        navigation.reset({
          index: 0,
          routes: [{ name: "Login" }],
        });
      } catch (error) {
        return false;
      }
    };
  
    const profileFoodVendorName = async () => {
      const vendorName = await AsyncStorage.getItem("FoodVendorName");
      setNamedisplay(vendorName);
    };
  
    useEffect(() => {
      profileFoodVendorName();
    }, []);

    return(
        <ProfileView
        namedisplay = {namedisplay}
        removeData = {removeData}
        isLogoutPopUpVisible={isLogoutPopUpVisible}
        setIsLogoutPopUpVisible={setIsLogoutPopUpVisible}
        />
    )

}

export default ProfileScreen;