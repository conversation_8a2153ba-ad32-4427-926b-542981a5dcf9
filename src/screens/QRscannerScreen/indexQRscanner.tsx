import React, { useEffect, useState } from "react";
import { Dimensions, PermissionsAndroid, Platform } from "react-native";
import { httpPost } from "../../utils/http";
import Geolocation from "react-native-geolocation-service";
import moment from "moment";
import AsyncStorage from "@react-native-async-storage/async-storage";
import QRScaneerView from "./QRscannerView";
import { stringText } from "../../utils/stringConstants";

const QRscannerView = (props: any) => {
  const { navigation } = props;
  const [datetoday, setDatetoday] = useState<Date>(new Date());
  const [granted, setGranted] = useState<boolean>(false);

  const [showPopUp, setShowPopUp] = useState<boolean>(true);
  const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>("");
  const [errorHandlerVisibility, setErrorHandlerVisibility] =
    useState<boolean>(false);
  const [orderSuccessPopupVisibility, setOrderSuccessPopupVisibility] =
    useState<boolean>(false);
  const [orderSuccessPopupMessage, setOrderSuccessPopupMessage] =
    useState<string>("");

  const markerWidthPercentage = 50;
  const markerHeightPercentage = 25;
  const { width: screenWidth, height: screenHeight } = Dimensions.get("window");
  const markerWidth =
    (Math.min(screenWidth, screenHeight) * markerWidthPercentage) / 100;
  const markerHeight =
    (Math.min(screenWidth, screenHeight) * markerHeightPercentage) / 100;

  const orderSuccessPopupClicked = (isVisible: boolean, message: string) => {
    setOrderSuccessPopupVisibility(isVisible);
    setOrderSuccessPopupMessage(message);
    if (!isVisible) {
      setShowPopUp(true);
    }
  };

  const errorHandlerClicked = (
    errorHandlerVisibility: boolean,
    errorHandlerMessage: string
  ) => {
    setErrorHandlerVisibility(errorHandlerVisibility);
    setErrorHandlerMessage(errorHandlerMessage);
  };

  useEffect(() => {
    grant();
  }, []);

  const grant = async () => {
    try {
      if (Platform.OS === "ios") {
        const status = await Geolocation.requestAuthorization("whenInUse");
        if (status === "granted") {
          setGranted(true);
        } else {
          errorHandlerClicked(true, `${stringText.ConfirmLocation}`);
          setGranted(false);
        }
      } else {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
        );
        const camPermission = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA
        );
        if (
          granted === PermissionsAndroid.RESULTS.GRANTED &&
          camPermission === PermissionsAndroid.RESULTS.GRANTED
        ) {
          setGranted(true);
        } else {
          errorHandlerClicked(true, `${stringText.ConfirmLocation}`);
          setGranted(false);
        }
      }
    } catch (err) {
      setGranted(false);
    }
  };

  const dataConvert = (data: string) => {
    if (data.startsWith("{")) {
      GoToOrder(JSON.parse(data));
    } else {
      const lines = data.split("\n");
      const jsonObject = {};

      lines.forEach((line) => {
        const [key, value] = line.split(":").map((part) => part.trim());
        jsonObject[key] = value;
      });
      if(jsonObject && jsonObject?.ID){
      GoToOrder(jsonObject);
      }
    }
  };

  const makeSlideOutTranslation = (translationType: any) => {
    return {
      from: {
        [translationType]: screenHeight * -0.14,
      },
      to: {
        [translationType]: screenHeight * 0.15,
      },
    };
  };

  const GoToOrder = async (jsonObject: any) => {
    if (granted) {
      Geolocation.getCurrentPosition(
        (location) => {
          // AsyncStorage.getItem("DeviceToken").then((deviceToken) => {
          const payload = {
            order_type: `${jsonObject?.OrderType}`,
            type: `${jsonObject?.Type}`,
            user_id: parseInt(jsonObject?.ID),
            order_date: `${moment(datetoday).format("YYYY-MM-DD")}`,
            latitude: `${jsonObject?.Latitude}`,
            longitude: `${jsonObject?.Longitude}`,
            unique_key: `${jsonObject?.unique_key}`,
            file_name: `${jsonObject?.fileName}`,
            vendor_latitude: `${location?.coords?.latitude}`,
            vendor_longitude: `${location?.coords?.longitude}`,
            device_token: null,
            createdAt:`${jsonObject.createdAt}`
          };
          httpPost("/scan_qr_code/", payload)
            .then(async (response: any) => {
              orderSuccessPopupClicked(true, JSON.parse(response)?.message);
              setShowPopUp(false);
            })
            .catch((err: any) => {
              if (
                err?.message !== undefined &&
                err?.message !== null &&
                err?.message !== ""
              ) {
                errorHandlerClicked(true,err.response.data.message);
              } else {
                errorHandlerClicked(true, `${stringText.SomethingWentwrong}`);
              }
            });
          return location;
          // });
        },
        (err) => {
          errorHandlerClicked(true, `${stringText.ConfirmLocation}`);
        },
        { enableHighAccuracy: true, timeout: 15000 }
      );
    }
  };

  return (
    <QRScaneerView
      dataConvert={dataConvert}
      errorHandlerVisibility={errorHandlerVisibility}
      errorHandlerMessage={errorHandlerMessage}
      errorHandlerClicked={errorHandlerClicked}
      navigation={navigation}
      orderSuccessPopupVisibility={orderSuccessPopupVisibility}
      orderSuccessPopupMessage={orderSuccessPopupMessage}
      orderSuccessPopupClicked={orderSuccessPopupClicked}
      granted={granted}
      showPopUp={showPopUp}
      makeSlideOutTranslation={makeSlideOutTranslation}
      markerWidth={markerWidth}
      markerHeight={markerHeight}
    />
  );
};

export default QRscannerView;
