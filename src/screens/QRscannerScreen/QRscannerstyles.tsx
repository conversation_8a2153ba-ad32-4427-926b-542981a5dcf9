import { StyleSheet } from "react-native";
import { color } from "../../utils/color";

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  topView: {
    marginBottom: 50,
  },
  topText: {
    fontWeight: "400",
    fontSize: 25,
    borderBottomWidth: 1,
    color: color.DARK_BLUE,
    borderBottomColor: color.DARK_BLUE,
    fontFamily: "Poppins-Regular"
  },
  bottomView: {
    marginTop: 50,
  },
  bottomText: {
    color: color.DARK_BLUE,
  },
  marker: {
    borderColor: "white",
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.0)',
    borderTopWidth: 5
    // borderWidth: 3,
    // height: 500,
    // width: 500,
    // borderRadius: 30,
    // borderColor: color.DARK_BLUE
  },
  containerStyle: {
    justifyContent: 'center'
  },
  cameraTimeoutView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  cameraTimeoutText: {
    color: 'white',
    fontSize: 30,
    fontWeight: 'bold',
  },
  containerMarker: {
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderRightWidth: 5,
    borderLeftWidth: 5,
    borderTopWidth: 5,
    borderColor: 'transparent'
  },
  cornerTopLeft: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 100,
    height: 100,
    borderTopLeftRadius: 10,
    borderLeftWidth: 5,
    borderTopWidth: 5,
    borderBottomLeftRadius: 1,
    borderColor: 'white'
  },
  cornerTopRight: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 100,
    height: 100,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 1,
    borderTopWidth: 5,
    borderRightWidth: 5,
    borderColor: 'white',
  },
  cornerBottomLeft: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: 100,
    height: 100,
    borderBottomLeftRadius: 10,
    borderBottomWidth: 5,
    borderLeftWidth: 5,
    borderColor: 'white',
  },
  cornerBottomRight: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 100,
    height: 100,
    borderBottomRightRadius: 10,
    borderBottomWidth: 5,
    borderRightWidth: 5,
    borderColor: 'white',
  },
  containerMarker1: {
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    borderRightWidth: 5,
    borderLeftWidth: 5,
    borderBottomWidth: 5,
    borderColor: 'transparent',
  },
});

export default styles;