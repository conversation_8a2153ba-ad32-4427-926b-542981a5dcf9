import React from "react";
import {
    Image,
    SafeAreaView,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from "react-native";
import DatePicker from "react-native-date-picker";
import moment from "moment";
import Spinner from "../../component/Loader";
import { Table, Row, Rows } from "react-native-table-component";
import ErrorHandlerPopup from "../../component/ErrorHandlerPopup";
import styles from "./stylesHistory";
import { color } from "../../utils/color";
import { stringText } from "../../utils/stringConstants";


export type Props = {
    datesearch: any,
    setOpenSearch: any,
    datelast: any,
    setOpenlast: any,
    opensearch: any,
    setDateSearch: any,
    openlast: any,
    setDateLast: any,
    tableData: any,
    fetching: any,
    errorHandlerVisibility: any;
    errorHandlerMessage: string;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
};

const HistoryView = (props: Props) => {

    const { datesearch, setOpenSearch, datelast, setOpenlast, opensearch, setDateSearch, openlast, setDateLast, tableData, fetching, errorHandlerVisibility, errorHandlerMessage, errorHandlerClicked } = props

    return (
        <SafeAreaView style={styles.mainView}>
            <ScrollView>
                <Text style={styles.textOrder}>{stringText.DinnerHistory}</Text>
                <View style={styles.textOrder}>
                    <Text style={styles.textK}>{stringText.selectDate}</Text>
                </View>
                <View style={styles.ButtonImg}>
                    <View style={styles.TextImg}>
                        <TextInput
                            value={`${moment(datesearch).format("DD/MM/YYYY")}`}
                            placeholder={stringText.SelectDate}
                            textAlign="center"
                            style={styles.inputText}
                            onPressIn={() => setOpenSearch(true)}
                        />
                        <TouchableOpacity
                            onPress={() => setOpenSearch(true)}
                            style={styles.ImgView}
                        >
                            <Image
                                source={require("../../assets/Images/calender.png")}
                                style={styles.calenderView}
                            />
                        </TouchableOpacity>
                    </View>
                    <View style={styles.TextImg1}>
                        <TextInput
                            value={`${moment(datelast).format("DD/MM/YYYY")}`}
                            placeholder={stringText.LastDate}
                            textAlign="center"
                            style={styles.inputText}
                            onPressIn={() => setOpenlast(true)}
                        />
                        <TouchableOpacity
                            onPress={() => setOpenlast(true)}
                            style={styles.ImgView}
                        >
                            <Image
                                source={require("../../assets/Images/calender.png")}
                                style={styles.calenderView}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
                <DatePicker
                    modal
                    open={opensearch}
                    date={datesearch}
                    onConfirm={(date) => {
                        setOpenSearch(false);
                        setDateSearch(date);
                    }}
                    onCancel={() => {
                        setOpenSearch(false);
                    }}
                    mode="date"
                />
                <DatePicker
                    modal
                    open={openlast}
                    date={datelast}
                    onConfirm={(date) => {
                        setOpenlast(false);
                        setDateLast(date);
                    }}
                    onCancel={() => {
                        setOpenlast(false);
                    }}
                    mode="date"
                />
                {tableData?.tableData?.length > 0 && (
                    <View style={{ marginTop: 20, marginHorizontal: 20, marginBottom: 20 }}>
                        <Table
                            borderStyle={{
                                borderWidth: 1,
                                borderColor: color.ACCENT_BLUE,
                            }}
                        >
                            <Row
                                data={tableData.tableHead}
                                style={styles.head}
                                textStyle={styles.headText}
                            />
                            <Rows
                                data={tableData.tableData}
                                style={{}}
                                textStyle={styles.text}
                            />
                        </Table>
                    </View>
                )}
                <ErrorHandlerPopup
                    visible={errorHandlerVisibility}
                    errorHandlerMessage={errorHandlerMessage}
                    errorHandlerClicked={errorHandlerClicked}
                />
            </ScrollView>
            {
                !(tableData?.tableData?.length > 0) && !fetching &&
                <View
                    style={styles.noDataView}
                >
                    <Text
                        style={{
                            color: color.BLACK,
                            fontSize: 14,
                            fontWeight: '400',
                            fontFamily: 'Poppins-Regular'
                        }}
                    >
                        {stringText.DataNotAvailable}
                    </Text>
                </View>
            }
            <Spinner animating={fetching} />
        </SafeAreaView>
    )
}

export default HistoryView;