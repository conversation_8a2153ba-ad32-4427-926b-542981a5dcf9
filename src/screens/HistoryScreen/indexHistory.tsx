import React, { useEffect, useState } from "react";
import { Text,View} from "react-native";
import moment from "moment";
import { httpGet } from "../../utils/http";
import HistoryView from "./HistoryView";
import styles from "./stylesHistory";
import { color } from "../../utils/color";
import { stringText } from "../../utils/stringConstants";
import { OrderStatus } from "../../utils/enumConstants";


const HistoryScreen = () => {
    const [datesearch, setDateSearch] = useState(new Date());
    const [datelast, setDateLast] = useState(new Date());
    const [opensearch, setOpenSearch] = useState(false);
    const [openlast, setOpenlast] = useState(false);
    const [fetching, setFetching] = useState(false);
    const [tableData, setTableData] = useState<any>();
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>("");
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);

    useEffect(() => {
        LoadHistory();
    }, []);

    useEffect(() => {
        LoadHistory();
    }, [datesearch, datelast]);

    const LoadHistory = () => {
        if (datesearch?.valueOf() > datelast?.valueOf()) {
            errorHandlerClicked(true, `${stringText.SelectCorrectDate}`);
        } else {
            setFetching(true);
            httpGet(
                `/api/vendor_food_order_history?order_type=2&from_date=${moment(
                    datesearch
                ).format("YYYY-MM-DD")}&to_date=${moment(datelast).format(
                    "YYYY-MM-DD"
                )}`
            )
                .then((response: any) => {
                    const tableColumn: Array<any> = [];
                    if (JSON.parse(response)?.data?.length > 0) {
                    JSON.parse(response)?.data?.map((item: any, index: number) => {
                        const tempDate = item?.order_date?.split(" ")?.[0]?.split("-");
                        const date = `${tempDate?.[2]}/${tempDate?.[1]}/${tempDate?.[0]}`;
                        const innerItem = [date, item?.id, getOrderStatusViewCell(item?.status)];
                        tableColumn?.push(innerItem);
                    });
                    }

                    const tableData = {
                        tableHead: [`${stringText.DateText}`, `${stringText.OrderIDText}`,`${stringText.Status}`],
                        tableData: tableColumn,
                    };
                    setTableData(tableData);
                    setFetching(false);
                })
                .catch((err: any) => {
                    setFetching(false);
                    if (
                        err?.response?.data?.message !== undefined &&
                        err?.response?.data?.message !== null &&
                        err?.response?.data?.message !== ""
                    ) {
                        errorHandlerClicked(true, err?.response?.data?.message);
                    } else {
                        errorHandlerClicked(
                            true,
                            `${stringText.SomethingWentwrong}`
                        );
                    }
                });
        }
    };

    const getOrderStatusViewCell = (status: string) => {
        return (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row'
                }}
            >
                <Text
                    style={[
                        styles.text,
                        status === OrderStatus.CANCELLED
                            ? { color: color.DARK_RED }
                            : { color: color.DARK_GREEN }
                    ]}
                >
                    {status}
                </Text>
            </View>
        );
    };

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    return (
        <HistoryView
            datesearch={datesearch}
            setOpenSearch={setOpenSearch}
            datelast={datelast}
            setOpenlast={setOpenlast}
            opensearch={opensearch}
            setDateSearch={setDateSearch}
            openlast={openlast}
            setDateLast={setDateLast}
            tableData={tableData}
            fetching={fetching}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
        />

    )
}

export default HistoryScreen;