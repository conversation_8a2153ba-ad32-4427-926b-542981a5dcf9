import React from "react";
import {
  SafeAreaView, Text, TouchableOpacity, View, Image
} from "react-native";
import { stringText } from "../../utils/stringConstants";
import styles from "./styles";

export type Props = {
  GoToScanner: any
};

const HomeScreenView = (props: Props) => {

  const { GoToScanner } = props

  return (
    <SafeAreaView>
      <View style={styles.ImgView}>
        <View>
          <Image
            source={require("../../assets/Images/logokuber.png")}
            style={styles.logoImg}
          />
        </View>
        <View>
          <Text style={styles.textHead}>{stringText.Welcome}</Text>
        </View>
        <View>
          <TouchableOpacity
            style={styles.buttonView}
            onPress={() => GoToScanner()}
          >
            <Text style={styles.textButton}>{stringText.TaptoScan}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

export default HomeScreenView;