import React from "react";
import { Image, SafeAreaView, Text, TextInput, TouchableOpacity, View, Platform, Linking } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Spinner from "../../component/Loader";
import ErrorHandlerPopup from "../../component/ErrorHandlerPopup";
import styles from "./LoginStyles";
import { color } from "../../utils/color";
import { stringText } from "../../utils/stringConstants";
import RNImage from '../../component/RNImage';
import { Icon } from "react-native-elements/dist/icons/Icon";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

export type Props = {
    emailError: any,
    passwordErrorMessage: any,
    getUserName: any,
    getPassWord: any,
    loggedIn: any,
    loader: boolean,
    errorHandlerVisibility: any,
    errorHandlerMessage: any,
    errorHandlerClicked: any,
    showPassword:any,
    toggleShowPassword:any
}

const LoginScreenView = (props: Props) => {

    const { emailError, passwordErrorMessage, getUserName, getPassWord, loggedIn, loader, errorHandlerVisibility, errorHandlerMessage, errorHandlerClicked,
        showPassword, toggleShowPassword } = props

    const CopyRightString = stringText.CopyRightString;
    const CopyRightStringArray = CopyRightString.split(' ');

    return (
        <>
            <KeyboardAwareScrollView style={styles.mainView}>
                <SafeAreaView>
                    <Image
                        source={require("../../assets/Images/logokuber.png")}
                        resizeMode="contain"
                        style={styles.imageView}
                    />
                    <View style={[styles.otherLogo, Platform.isPad && { width: '60%', alignSelf: 'center', marginTop: 150 }]}>
                        <Text style={styles.textHead}>{stringText.EmailText}</Text>
                        <View
                            style={[
                                styles.EmailInput,
                                emailError.length > 0 && {
                                    borderColor: color.DARK_RED
                                }
                            ]}
                        >
                            <RNImage
                                source={require('../../assets/Images/Emailicon.png')}
                                style={styles.EmailIcon}
                            />
                            <TextInput
                                style={[
                                    styles.emailId,
                                    emailError.length > 0 && {
                                        borderColor: color.DARK_RED,
                                    },
                                ]}
                                returnKeyType="done"
                                keyboardType="email-address"
                                onChangeText={(text: string) => getUserName(text)}
                            />
                        </View>
                        {emailError && <Text style={styles.textPass}>{emailError}</Text>}
                        <View style={styles.MarginView}>
                            <Text style={styles.textHead}>{stringText.PasswordText}</Text>
                            <View
                                style={[
                                    styles.EmailInput,
                                    passwordErrorMessage.length > 0 && {
                                        borderColor: color.DARK_RED
                                    }
                                ]}
                            >
                                <RNImage
                                    source={require('../../assets/Images/Passwordicon.png')}
                                    style={styles.PasswordIcon}
                                />
                                <TextInput
                                    style={[
                                        styles.passWord,
                                        passwordErrorMessage.length > 0 && {
                                            borderColor: color.DARK_RED,
                                        },
                                    ]}
                                    onChangeText={(passWord: string) => getPassWord(passWord)}
                                    autoCapitalize="none"
                                    autoCorrect={false}
                                    secureTextEntry={!showPassword}
                                    // secureTextEntry={true}
                                    returnKeyType="go"
                                    placeholderTextColor={color.BLACK}
                                />
                                <MaterialCommunityIcons
                                    name={showPassword ? 'eye-off' : 'eye'}
                                    size={24}
                                    color="#aaa"
                                    style={styles.icon}
                                    onPress={toggleShowPassword}

                                />
                            </View>
                            <View>
                                <View>
                                    {passwordErrorMessage && (
                                        <Text style={styles.textPass}>{passwordErrorMessage}</Text>
                                    )}
                                </View>
                            </View>
                        </View>
                        <View style={styles.logButton}>
                            <TouchableOpacity
                                style={styles.Button}
                                onPress={() => loggedIn()}
                            >
                                <Text style={styles.textButton}>{stringText.LoginText}</Text>
                            </TouchableOpacity>
                        </View>
                        <Text style={[styles.copyRightText, { textAlign: 'center', marginTop: 10 }]}>
                            {CopyRightStringArray?.map((text: string) => {
                                if (text === 'XXXXX.') {
                                    return (
                                        <Text onPress={() => { Linking.openURL('https://tudip.com/') }} style={[styles.copyRightText, { color: '#C11728' }]}>{stringText.TudipTechnologies}{` `}</Text>
                                    );
                                }
                                if (text === 'YYYYY.') {
                                    return (
                                        <Text onPress={() => { Linking.openURL('https://tudip.com/privacy-policy/') }} style={[styles.copyRightText, { color: '#C11728' }]}>{stringText.PrivacyPolicy}{` `}</Text>
                                    );
                                }
                                return (
                                    <Text style={styles.copyRightText}>{text}{` `}</Text>
                                );
                            })}
                        </Text>
                    </View>
                </SafeAreaView>
            </KeyboardAwareScrollView>
            <Spinner animating={loader} />
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </>
    )
}

export default LoginScreenView;