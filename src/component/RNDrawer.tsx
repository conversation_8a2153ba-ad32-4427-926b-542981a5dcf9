import React from "react";
import { View, Image, StyleSheet, Text } from "react-native";

const RNDrawer = (props: any) => {
  const { label, source } = props;
  return (
    <View style={Style.RNDrawerItemContainer}>
      <Image style={{ height: 25, width: 25 }} source={source} />
      <Text style={Style.RNDrawerTextStyle}>{label}</Text>
    </View>
  );
};
export default RNDrawer;

const Style = StyleSheet.create({
  RNDrawerItemContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  RNDrawerTextStyle: {
    color: "black",
    fontSize: 15,
    fontWeight: "400",
    fontFamily: "Poppins-Regular",
  },
});
