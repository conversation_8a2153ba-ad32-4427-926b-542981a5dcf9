import { StyleSheet } from "react-native";
import { color } from '../utils/color';

const styles = StyleSheet.create({
    LogOutPopup: {
        backgroundColor: "rgba(0,0,0,0)",
        flex: 1,
        flexDirection: "column",
        justifyContent: "center",
    },
    LogOutPopUpCardView: {
        alignSelf: "center",
        backgroundColor: color.WHITE,
        width: "90%",
        justifyContent: "center",
        alignItems: "center",
        paddingTop: 9,
        paddingBottom: 12,
        paddingHorizontal: 9,
    },
    LogOutTextPopup: {
        marginTop: 9,
        color: color.GREY_COLOR,
        fontWeight: "400",
        fontSize: 14,
        fontFamily: "Poppins-Regular",
    },
    LogOutPopUpButtonView: {
        flexDirection: "row",
        marginTop: 15
    },
    NoTextView: {
        flex: 1,
        backgroundColor: color.WHITE,
        justifyContent: "center",
        alignItems: "center",
        marginRight: 10,
        borderRadius: 4,
        paddingVertical: 7,
        borderColor: color.DARK_BLUE,
        borderWidth: 1,
    },
    NoTextStyles: {
        color: color.DARK_BLUE,
        fontWeight: "400",
        fontSize: 13,
        fontFamily: "Poppins-Regular",
    },
    LogOutPopUpYesView: {
        flex: 1,
        backgroundColor: color.DARK_BLUE,
        justifyContent: "center",
        alignItems: "center",
        marginLeft: 10,
        borderRadius: 4,
        paddingVertical: 7,
    },
    LogOutTextStyles: {
        color: color.WHITE,
        fontWeight: "400",
        fontSize: 13,
        fontFamily: "Poppins-Regular",
    },

});

export default styles;
