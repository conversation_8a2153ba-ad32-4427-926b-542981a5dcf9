import React from "react";
import { ActivityIndicator, StyleSheet, View } from "react-native";
import { color } from "../utils/color";

export type Props = {
  animating: boolean;
};

const RNActivityIndicator = (props: any) => {
  const { animating } = props;
  return (
    <>
      {animating && (
        <View style={style.activityIndicatorContainer}>
          <ActivityIndicator
            animating={animating}
            size="large"
            color={color.ACCENT_BLUE}
            style={animating ? style.spinnerStyle : style.spinnerStyle1}
          />
        </View>
      )}
    </>
  );
};

export default RNActivityIndicator;

const style = StyleSheet.create({
  activityIndicatorContainer: {
    height: "100%",
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
  },
  spinnerStyle: {
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    opacity: 0.8,
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0,0,0,0)",
  },
  spinnerStyle1: {
    alignItems: "center",
    justifyContent: "center",
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
});
