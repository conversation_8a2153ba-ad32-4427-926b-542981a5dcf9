import React, { <PERSON> } from "react";
import { Modal, View, Text, TouchableOpacity } from "react-native";
import { color } from "../utils/color";
import { stringText } from "../utils/stringConstants";
import { Card } from "@rneui/base";

type Props = {
  visible?: boolean;
  orderSuccessMessage: string;
  orderSuccessPopupClicked: (isVisible: boolean, message: string) => void;
  navigation: any;
};

const OrderSuccessPopup: FC<Props> = (props) => {
  const { visible, orderSuccessMessage, orderSuccessPopupClicked, navigation } =
    props;

  return (
    <Modal transparent={visible} visible={visible} animationType="fade">
      <View
        style={{
          backgroundColor: "rgba(0,0,0,0)",
          flex: 1,
          flexDirection: "column",
          justifyContent: "center",
        }}
      >
        <Card
          wrapperStyle={{
            alignSelf: "center",
            backgroundColor: color.WHITE,
            width: "90%",
            justifyContent: "center",
            alignItems: "center",
            paddingTop: 9,
            paddingBottom: 12,
            paddingHorizontal: 9,
          }}
          containerStyle={{
            elevation: 4,
            padding: 0,
            borderRadius: 4,
            margin: 0,
          }}
        >
          <Text
            style={{
              marginTop: 9,
              color: color.GREY_COLOR,
              fontWeight: "500",
              fontSize: 16,
              fontFamily: "Poppins-Regular",
            }}
          >
            {orderSuccessMessage}
          </Text>
          <Text
            style={{
              marginTop: 9,
              color: color.GREY_COLOR,
              fontWeight: "400",
              fontSize: 14,
              fontFamily: "Poppins-Regular",
            }}
          >
            {stringText.BackText}
          </Text>
          <View style={{ flexDirection: "row", marginTop: 15 }}>
            <TouchableOpacity
              onPress={() => {
                orderSuccessPopupClicked(false, "");
              }}
              style={{
                flex: 1,
                backgroundColor: color.WHITE,
                justifyContent: "center",
                alignItems: "center",
                marginRight: 10,
                borderRadius: 4,
                paddingVertical: 7,
                borderColor: color.DARK_BLUE,
                borderWidth: 1,
              }}
            >
              <Text
                style={{
                  color: color.DARK_BLUE,
                  fontWeight: "400",
                  fontSize: 13,
                  fontFamily: "Poppins-Regular",
                }}
              >
                {stringText.NoText}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                orderSuccessPopupClicked(false, "");
                navigation.goBack();
              }}
              style={{
                flex: 1,
                backgroundColor: color.DARK_BLUE,
                justifyContent: "center",
                alignItems: "center",
                marginLeft: 10,
                borderRadius: 4,
                paddingVertical: 7,
              }}
            >
              <Text
                style={{
                  color: color.WHITE,
                  fontWeight: "400",
                  fontSize: 13,
                  fontFamily: "Poppins-Regular",
                }}
              >
                {stringText.YesText}
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      </View>
    </Modal>
  );
};

export default OrderSuccessPopup;
