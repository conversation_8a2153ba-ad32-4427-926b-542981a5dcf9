import { stringText } from "./stringConstants";

export const validation: any = () => ({
  Email: {
    presence: {
      allowEmpty: false,
      message: `${stringText.EmailErrorMessage}`,
    },
    format: {
      pattern:
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      message: `${stringText.IncorrectEmail}`,
    },
  },
});
