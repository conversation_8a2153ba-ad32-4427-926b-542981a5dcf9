export const stringText = {

    EmailText: "Email",
    PasswordText: "Password",
    DashboardTittle: "Tudipversaries &",
    DashboardTittleBirthday: "Birthday’s",
    AnniversaryGreeting: "Happy Tudipversary!",
    BirthdayGreeting: "Happy Birthday Tudipians!",
    LunchTittle: "Lunch",
    DinnerTittle: "Dinner",
    DinnerHistory: "Dinner Order History",
    orderDate: "Order date",
    DataNotAvailable: "No Data Available",
    OrderCompleted: "Your order is already completed.",
    ScanOrder: "Please scan code to place order",
    GenerateQRcode: "Generate QR code for",
    InText: "In",
    OutText: "Out",
    QRcodeText: "Get QR code here",
    LogoutTextPopUp: "Do you want to Logout?",
    BackText: "Do you want to go back?",
    NoText: "No",
    YesText: "Yes",
    EmpText: "EMP ID:",
    LogoutText: "Logout",
    LoginText: "Login",
    AttendanceHistory: "Your Attendance History",
    selectDate: "Please select date",
    SelectCorrectDate: "Please select the date in correct range.",
    SomethingWentwrong: "Please try again later or check your network",
    ConfirmLocation: "Please confirm you have enabled your device location.",
    SelectDate: "Search date",
    LastDate: "Last Date",
    EmailErrorMessage: "Please Enter Email",
    PasswordErrorMessage: "Please Enter Password",
    IncorrectEmail: "Incorrect email",
    DateText: "Date",
    InTime: "In Time",
    OutTime: "Out Time",
    TotalHrs: "Total Hrs",
    ViewLogs: "View Logs",
    OrderIDText: "Order ID",
    Status: "Status",
    Welcome: "Welcome!",
    TaptoScan: "Tap to scan",
    ScanTo: "Scan Here",
    NoCameraPermission: "No camera permission granted",
    CopyRightString: "Copyright © 2023 by XXXXX. \n All Rights Reserved.\n YYYYY.",
    TudipTechnologies: "Tudip Technologies.",
    PrivacyPolicy: "Privacy Policy."
}