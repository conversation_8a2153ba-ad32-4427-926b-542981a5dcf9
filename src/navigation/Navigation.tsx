import React from "react";
import {
  createNavigationContainerRef,
  NavigationContainer,
} from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import Login from "../screens/LoginScreen/indexLogin";
import QRscannerView from "../screens/QRscannerScreen/indexQRscanner";
import Home from "../screens/HomeScreen/index";
import Profile from "../screens/ProfileScreen/indexProfile";
import History from "../screens/HistoryScreen/indexHistory";
import { TouchableOpacity } from "react-native";
import Entypo from "react-native-vector-icons/Entypo";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import styles from "./styles";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { color } from "../utils/color";

const Stack = createNativeStackNavigator();
export var navigationRef = createNavigationContainerRef();

const Navigation = (props: any) => {

  const { navigation } = props;

  const GoToProfile = () => {
    navigationRef.navigate("Profile");
  };
  const GoToHistory = () => {
    navigationRef.navigate("History");
  };

  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator initialRouteName="Login">
        <Stack.Screen
          name="Login"
          component={Login}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Home"
          component={Home}
          options={{
            headerShown: true,
            headerRight: () => (
              <TouchableOpacity
                onPress={() => GoToProfile()}>
                <FontAwesome
                  name="user-circle"
                  color={color.DARK_BLUE}
                  size={30}
                  style={styles.proImg}
                />
              </TouchableOpacity>
            ),
            // headerLeft: () => (
            //   <TouchableOpacity
            //     onPress={() => GoToHistory()}
            //     style={{ marginVertical: 10, marginRight: 20 }}
            //     >
            //     <MaterialCommunityIcons
            //       name="history"
            //       color={color.DARK_BLUE}
            //       size={30}
            //     />
            //   </TouchableOpacity>
            // ),
            headerTitleAlign: 'left',
            headerShadowVisible: true,
            headerTitleStyle: {
              fontFamily: "Poppins-Regular",
              fontWeight: "700",
              fontSize: 18,
              color: color.GREY_COLOR,
            },
            headerStyle: { backgroundColor: color.WHITE },
          }}
        />
        <Stack.Screen
          name="QRscannerView"
          component={QRscannerView}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Profile"
          component={Profile}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="History"
          component={History}
          options={{
            headerShown: true,
            headerLeft: () => (
              <TouchableOpacity
                onPress={() => navigationRef.goBack()}
                style={{ marginVertical: 10, marginRight: 20 }}
              >
                <Entypo name="chevron-left" color={color.DARK_BLUE} size={24} />
              </TouchableOpacity>
            ),
            headerTitleStyle: {
              fontFamily: "Poppins-Regular",
              fontWeight: "700",
              fontSize: 18,
              color: color.GREY_COLOR,
            },
            headerStyle: { backgroundColor: color.WHITE },
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default Navigation;